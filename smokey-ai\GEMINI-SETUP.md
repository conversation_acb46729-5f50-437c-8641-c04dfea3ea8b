# 🤖 Google Gemini API Setup Guide

## 🚀 Quick Setup (2 minutes)

### Step 1: Get Your FREE Gemini API Key
1. **Go to:** https://makersuite.google.com/app/apikey
2. **Sign in** with your Google account
3. **Click "Create API Key"**
4. **Copy the key** (starts with `AIza...`)

### Step 2: Add Your API Key
1. **Open:** `smokey-ai/config.js`
2. **Find line 72:** `apiKey: 'YOUR_GEMINI_API_KEY_HERE',`
3. **Replace** `YOUR_GEMINI_API_KEY_HERE` with your actual key
4. **Save the file**

### Example:
```javascript
gemini: {
    enabled: true,
    apiKey: 'AIzaSyDXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX', // Your actual key here
    endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent',
    // ... rest of config
},
```

## ✅ Current Status

**Your Smokey AI currently has:**
- ✅ **Groq API** - Working (fast responses)
- ✅ **DeepSeek API** - Working (coding tasks)
- ⚠️ **Gemini API** - Ready to add (just need your key)
- ✅ **Image Generation** - Working
- ✅ **Send Message** - Fixed and working!

## 🎯 How It Works

**Smart API Routing:**
1. **Groq** (fastest) - Used first for general chat
2. **DeepSeek** - Used for coding questions
3. **Gemini** - Available as additional option
4. **Fallback** - Friendly message if APIs are down

## 🔧 Alternative: Use Without Gemini

**Your app works perfectly right now with:**
- **Groq API** for fast, smart responses
- **DeepSeek API** for coding help
- **Emergency fallback** messages

**No Gemini key needed** - your app is fully functional!

## 🧪 Test Your Setup

1. **Open:** `smokey-ai/index.html`
2. **Type:** "Hello, how are you?"
3. **Press Enter**
4. **See:** Real AI response from Groq! 🎉

## 📋 Troubleshooting

**If responses don't work:**
1. Check browser console (F12) for errors
2. Verify API keys in `config.js`
3. Check internet connection
4. Try refreshing the page

**API Key Formats:**
- **Groq:** `gsk_...` (working ✅)
- **DeepSeek:** `sk-...` (working ✅)  
- **Gemini:** `AIza...` (optional)

## 🎉 You're All Set!

Your Smokey AI is now fully functional with:
- ✅ Working send message functionality
- ✅ Real AI responses via Groq/DeepSeek
- ✅ All UI bugs fixed
- ✅ Profile, tools, and image generator working

**Just start chatting and enjoy your AI assistant!** 🐾
