<html>
  <head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Spline+Sans%3Awght%40400%3B500%3B700"
    />

    <title>Stitch Design</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
  </head>
  <body>
    <div class="relative flex size-full min-h-screen flex-col bg-[#101a23] dark group/design-root overflow-x-hidden" style='font-family: "S<PERSON><PERSON> Sans", "Noto Sans", sans-serif;'>
      <div class="layout-container flex h-full grow flex-col">
        <header class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#223649] px-10 py-3">
          <div class="flex items-center gap-4 text-white">
            <div class="size-4">
              <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M36.7273 44C33.9891 44 31.6043 39.8386 30.3636 33.69C29.123 39.8386 26.7382 44 24 44C21.2618 44 18.877 39.8386 17.6364 33.69C16.3957 39.8386 14.0109 44 11.2727 44C7.25611 44 4 35.0457 4 24C4 12.9543 7.25611 4 11.2727 4C14.0109 4 16.3957 8.16144 17.6364 14.31C18.877 8.16144 21.2618 4 24 4C26.7382 4 29.123 8.16144 30.3636 14.31C31.6043 8.16144 33.9891 4 36.7273 4C40.7439 4 44 12.9543 44 24C44 35.0457 40.7439 44 36.7273 44Z"
                  fill="currentColor"
                ></path>
              </svg>
            </div>
            <h2 class="text-white text-lg font-bold leading-tight tracking-[-0.015em]">Smokey AI</h2>
          </div>
          <div class="flex flex-1 justify-end gap-8">
            <div class="flex items-center gap-9">
              <a class="text-white text-sm font-medium leading-normal" href="#">Product</a>
              <a class="text-white text-sm font-medium leading-normal" href="#">Solutions</a>
              <a class="text-white text-sm font-medium leading-normal" href="#">Pricing</a>
              <a class="text-white text-sm font-medium leading-normal" href="#">Resources</a>
            </div>
            <button
              class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#3d98f4] text-white text-sm font-bold leading-normal tracking-[0.015em]"
            >
              <span class="truncate">Get started</span>
            </button>
          </div>
        </header>
        <div class="px-40 flex flex-1 justify-center py-5">
          <div class="layout-content-container flex flex-col max-w-[960px] flex-1">
            <div class="@container">
              <div class="@[480px]:p-4">
                <div
                  class="flex min-h-[480px] flex-col gap-6 bg-cover bg-center bg-no-repeat @[480px]:gap-8 @[480px]:rounded-xl items-center justify-center p-4"
                  style='background-image: linear-gradient(rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.4) 100%), url("https://lh3.googleusercontent.com/aida-public/AB6AXuBWdgOodLLPUI0hu13I--deO0I6hwzt83ycDTffamuvBKZ6QiYiQ7ofZ4GBwuC-z3jEXXNb3paMCI8mDnM2J8dexZ2U0dWeGBW7Egv_bRqdSnORB6cqOpEgCDEh2-7QpiSTyN7gpqE02WuLISdHzQs4yuHkhrNhTmtPF7lc5HgsyoXyImCzPETQS8wuBgyaXANtTyskbgXhIFdmF0zijY5gIYdNyEOWIqyr2towtgiOYvixbZ3zZnS5uFqSLPv-RnvIh8Mnfx2pud49");'
                >
                  <div class="flex flex-col gap-2 text-center">
                    <h1
                      class="text-white text-4xl font-black leading-tight tracking-[-0.033em] @[480px]:text-5xl @[480px]:font-black @[480px]:leading-tight @[480px]:tracking-[-0.033em]"
                    >
                      Meet Smokey AI
                    </h1>
                    <h2 class="text-white text-sm font-normal leading-normal @[480px]:text-base @[480px]:font-normal @[480px]:leading-normal">
                      Your friendly AI companion, always ready to chat and assist. Powered by advanced AI, Smokey AI brings a touch of feline charm to your digital interactions.
                    </h2>
                  </div>
                  <button
                    class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 @[480px]:h-12 @[480px]:px-5 bg-[#3d98f4] text-white text-sm font-bold leading-normal tracking-[0.015em] @[480px]:text-base @[480px]:font-bold @[480px]:leading-normal @[480px]:tracking-[0.015em]"
                  >
                    <span class="truncate">Get started</span>
                  </button>
                </div>
              </div>
            </div>
            <div class="flex flex-col gap-10 px-4 py-10 @container">
              <div class="flex flex-col gap-4">
                <h1
                  class="text-white tracking-light text-[32px] font-bold leading-tight @[480px]:text-4xl @[480px]:font-black @[480px]:leading-tight @[480px]:tracking-[-0.033em] max-w-[720px]"
                >
                  What Smokey AI can do
                </h1>
                <p class="text-white text-base font-normal leading-normal max-w-[720px]">
                  Smokey AI is designed to be versatile and helpful, adapting to your needs with ease. Here are some of the ways Smokey AI can assist you:
                </p>
              </div>
              <div class="grid grid-cols-[repeat(auto-fit,minmax(158px,1fr))] gap-3 p-0">
                <div class="flex flex-1 gap-3 rounded-lg border border-[#314d68] bg-[#182634] p-4 flex-col">
                  <div class="text-white" data-icon="ChatCircleDots" data-size="24px" data-weight="regular">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                      <path
                        d="M140,128a12,12,0,1,1-12-12A12,12,0,0,1,140,128ZM84,116a12,12,0,1,0,12,12A12,12,0,0,0,84,116Zm88,0a12,12,0,1,0,12,12A12,12,0,0,0,172,116Zm60,12A104,104,0,0,1,79.12,219.82L45.07,231.17a16,16,0,0,1-20.24-20.24l11.35-34.05A104,104,0,1,1,232,128Zm-16,0A88,88,0,1,0,51.81,172.06a8,8,0,0,1,.66,6.54L40,216,77.4,203.53a7.85,7.85,0,0,1,2.53-.42,8,8,0,0,1,4,1.08A88,88,0,0,0,216,128Z"
                      ></path>
                    </svg>
                  </div>
                  <div class="flex flex-col gap-1">
                    <h2 class="text-white text-base font-bold leading-tight">Engaging Conversations</h2>
                    <p class="text-[#90adcb] text-sm font-normal leading-normal">
                      Enjoy natural and engaging conversations with Smokey AI, designed to feel like chatting with a friend.
                    </p>
                  </div>
                </div>
                <div class="flex flex-1 gap-3 rounded-lg border border-[#314d68] bg-[#182634] p-4 flex-col">
                  <div class="text-white" data-icon="MagicWand" data-size="24px" data-weight="regular">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                      <path
                        d="M48,64a8,8,0,0,1,8-8H72V40a8,8,0,0,1,16,0V56h16a8,8,0,0,1,0,16H88V88a8,8,0,0,1-16,0V72H56A8,8,0,0,1,48,64ZM184,192h-8v-8a8,8,0,0,0-16,0v8h-8a8,8,0,0,0,0,16h8v8a8,8,0,0,0,16,0v-8h8a8,8,0,0,0,0-16Zm56-48H224V128a8,8,0,0,0-16,0v16H192a8,8,0,0,0,0,16h16v16a8,8,0,0,0,16,0V160h16a8,8,0,0,0,0-16ZM219.31,80,80,219.31a16,16,0,0,1-22.62,0L36.68,198.63a16,16,0,0,1,0-22.63L176,36.69a16,16,0,0,1,22.63,0l20.68,20.68A16,16,0,0,1,219.31,80Zm-54.63,32L144,91.31l-96,96L68.68,208ZM208,68.69,187.31,48l-32,32L176,100.69Z"
                      ></path>
                    </svg>
                  </div>
                  <div class="flex flex-col gap-1">
                    <h2 class="text-white text-base font-bold leading-tight">Creative Assistance</h2>
                    <p class="text-[#90adcb] text-sm font-normal leading-normal">
                      From writing stories to brainstorming ideas, Smokey AI can help you unlock your creative potential.
                    </p>
                  </div>
                </div>
                <div class="flex flex-1 gap-3 rounded-lg border border-[#314d68] bg-[#182634] p-4 flex-col">
                  <div class="text-white" data-icon="Robot" data-size="24px" data-weight="regular">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                      <path
                        d="M200,48H136V16a8,8,0,0,0-16,0V48H56A32,32,0,0,0,24,80V192a32,32,0,0,0,32,32H200a32,32,0,0,0,32-32V80A32,32,0,0,0,200,48Zm16,144a16,16,0,0,1-16,16H56a16,16,0,0,1-16-16V80A16,16,0,0,1,56,64H200a16,16,0,0,1,16,16Zm-52-56H92a28,28,0,0,0,0,56h72a28,28,0,0,0,0-56Zm-28,16v24H120V152ZM80,164a12,12,0,0,1,12-12h12v24H92A12,12,0,0,1,80,164Zm84,12H152V152h12a12,12,0,0,1,0,24ZM72,108a12,12,0,1,1,12,12A12,12,0,0,1,72,108Zm88,0a12,12,0,1,1,12,12A12,12,0,0,1,160,108Z"
                      ></path>
                    </svg>
                  </div>
                  <div class="flex flex-col gap-1">
                    <h2 class="text-white text-base font-bold leading-tight">Smart Automation</h2>
                    <p class="text-[#90adcb] text-sm font-normal leading-normal">
                      Automate tasks and streamline your workflow with Smokey AI's intelligent automation capabilities.
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div class="flex flex-col gap-10 px-4 py-10 @container">
              <div class="flex flex-col gap-4">
                <h1
                  class="text-white tracking-light text-[32px] font-bold leading-tight @[480px]:text-4xl @[480px]:font-black @[480px]:leading-tight @[480px]:tracking-[-0.033em] max-w-[720px]"
                >
                  Why choose Smokey AI?
                </h1>
                <p class="text-white text-base font-normal leading-normal max-w-[720px]">
                  Smokey AI stands out with its unique blend of personality and functionality. Here's why users love Smokey AI:
                </p>
              </div>
              <div class="grid grid-cols-[repeat(auto-fit,minmax(158px,1fr))] gap-3">
                <div class="flex flex-col gap-3 pb-3">
                  <div
                    class="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-xl"
                    style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuCMoesIdRzQc42_LkgEz0YeSOwDj9b6QjkZlLE9Ni67Muu072cJZwoJUur4Tq47IBcM5FyJiQeC71RKLoMFIjNWbF3vb8ovt3O1j_2hx6nlmolk_ZJ_jmx3rki681go9nz1WixZSXzL3jPZTq7S_QiDQRRaWlGmt4vg9DGdWqluXPuqggOoFebp3LkW_hiXPEWHTAoWWekNTnwoCBeyxXe4CLMXIQRSiWwKoEYOFw6GrjldhNSEeKboj_qse3Z5YH8QvjuEuWqbAsVA");'
                  ></div>
                  <div>
                    <p class="text-white text-base font-medium leading-normal">Unique Personality</p>
                    <p class="text-[#90adcb] text-sm font-normal leading-normal">Smokey AI's charming feline personality adds a fun and engaging element to your interactions.</p>
                  </div>
                </div>
                <div class="flex flex-col gap-3 pb-3">
                  <div
                    class="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-xl"
                    style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuArU1ADtxrwzKH2knIIUnikXPSh_I3Mccgxm7nSoaRljkd9QShpA7BNxcab76klKmXIVwiL-qnvKr4aSgQ6z-P99t6lE-hQ2aZR9Aumuk7FjrtaArDFpMjemslyHTsTPYITgxU7zVMtYt1MPQCvSmnve9uQpkvt-uLGjoezdlajpQSSXCTtg2pPGLRWIzTF9KEE9QfTvl0JbFgugk4Wzt1FfT2riahQIdW_D6amNK8m1HgxcXMbIle1KXMaz7sCUJI0Sc2DfCCCzOHk");'
                  ></div>
                  <div>
                    <p class="text-white text-base font-medium leading-normal">Advanced AI</p>
                    <p class="text-[#90adcb] text-sm font-normal leading-normal">Powered by cutting-edge AI technology, Smokey AI delivers accurate and intelligent responses.</p>
                  </div>
                </div>
                <div class="flex flex-col gap-3 pb-3">
                  <div
                    class="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-xl"
                    style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDshoejrLGdgORMjd4tJi1IDT93sgorHznc9FMyyCLidBIKCyffD5WBX4MnwKcMIVZx9sSDE3KY5AmioK-MF2qav_CleUUW5qS5g3WIccJXNPHO6N-qVVylMMpjhL8fngsuOBVpYOIiv6j30pqgutim4AGtm0ltsMsGywNeazlI2KRizmI2VMcDvWqrZFk3xEEB5UBgzx4TwEYzYIC5sISv_vuvUCj_P0ZT__XkbsUK2JAzHT7dGhFu3mnYSTVgMV1KPqVQvaZQcE4L");'
                  ></div>
                  <div>
                    <p class="text-white text-base font-medium leading-normal">User-Friendly</p>
                    <p class="text-[#90adcb] text-sm font-normal leading-normal">Smokey AI is designed to be intuitive and easy to use, making it accessible for everyone.</p>
                  </div>
                </div>
              </div>
            </div>
            <div class="@container">
              <div class="flex flex-col justify-end gap-6 px-4 py-10 @[480px]:gap-8 @[480px]:px-10 @[480px]:py-20">
                <div class="flex flex-col gap-2 text-center">
                  <h1
                    class="text-white tracking-light text-[32px] font-bold leading-tight @[480px]:text-4xl @[480px]:font-black @[480px]:leading-tight @[480px]:tracking-[-0.033em] max-w-[720px]"
                  >
                    Ready to experience the magic of Smokey AI?
                  </h1>
                  <p class="text-white text-base font-normal leading-normal max-w-[720px">Join our community and start chatting with Smokey AI today!</p>
                </div>
                <div class="flex flex-1 justify-center">
                  <div class="flex justify-center">
                    <button
                      class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 @[480px]:h-12 @[480px]:px-5 bg-[#3d98f4] text-white text-sm font-bold leading-normal tracking-[0.015em] @[480px]:text-base @[480px]:font-bold @[480px]:leading-normal @[480px]:tracking-[0.015em] grow"
                    >
                      <span class="truncate">Get started</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <footer class="flex justify-center">
          <div class="flex max-w-[960px] flex-1 flex-col">
            <footer class="flex flex-col gap-6 px-5 py-10 text-center @container">
              <div class="flex flex-wrap items-center justify-center gap-6 @[480px]:flex-row @[480px]:justify-around">
                <a class="text-[#90adcb] text-base font-normal leading-normal min-w-40" href="#">Product</a>
                <a class="text-[#90adcb] text-base font-normal leading-normal min-w-40" href="#">Solutions</a>
                <a class="text-[#90adcb] text-base font-normal leading-normal min-w-40" href="#">Pricing</a>
                <a class="text-[#90adcb] text-base font-normal leading-normal min-w-40" href="#">Resources</a>
                <a class="text-[#90adcb] text-base font-normal leading-normal min-w-40" href="#">Contact</a>
              </div>
              <div class="flex flex-wrap justify-center gap-4">
                <a href="#">
                  <div class="text-[#90adcb]" data-icon="TwitterLogo" data-size="24px" data-weight="regular">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                      <path
                        d="M247.39,68.94A8,8,0,0,0,240,64H209.57A48.66,48.66,0,0,0,168.1,40a46.91,46.91,0,0,0-33.75,13.7A47.9,47.9,0,0,0,120,88v6.09C79.74,83.47,46.81,50.72,46.46,50.37a8,8,0,0,0-13.65,4.92c-4.31,47.79,9.57,79.77,22,98.18a110.93,110.93,0,0,0,21.88,24.2c-15.23,17.53-39.21,26.74-39.47,26.84a8,8,0,0,0-3.85,11.93c.75,1.12,3.75,5.05,11.08,8.72C53.51,229.7,65.48,232,80,232c70.67,0,129.72-54.42,135.75-124.44l29.91-29.9A8,8,0,0,0,247.39,68.94Zm-45,29.41a8,8,0,0,0-2.32,5.14C196,166.58,143.28,216,80,216c-10.56,0-18-1.4-23.22-3.08,11.51-6.25,27.56-17,37.88-32.48A8,8,0,0,0,92,169.08c-.47-.27-43.91-26.34-44-96,16,13,45.25,33.17,78.67,38.79A8,8,0,0,0,136,104V88a32,32,0,0,1,9.6-22.92A30.94,30.94,0,0,1,167.9,56c12.66.16,24.49,7.88,29.44,19.21A8,8,0,0,0,204.67,80h16Z"
                      ></path>
                    </svg>
                  </div>
                </a>
                <a href="#">
                  <div class="text-[#90adcb]" data-icon="InstagramLogo" data-size="24px" data-weight="regular">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                      <path
                        d="M128,80a48,48,0,1,0,48,48A48.05,48.05,0,0,0,128,80Zm0,80a32,32,0,1,1,32-32A32,32,0,0,1,128,160ZM176,24H80A56.06,56.06,0,0,0,24,80v96a56.06,56.06,0,0,0,56,56h96a56.06,56.06,0,0,0,56-56V80A56.06,56.06,0,0,0,176,24Zm40,152a40,40,0,0,1-40,40H80a40,40,0,0,1-40-40V80A40,40,0,0,1,80,40h96a40,40,0,0,1,40,40ZM192,76a12,12,0,1,1-12-12A12,12,0,0,1,192,76Z"
                      ></path>
                    </svg>
                  </div>
                </a>
                <a href="#">
                  <div class="text-[#90adcb]" data-icon="FacebookLogo" data-size="24px" data-weight="regular">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                      <path
                        d="M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm8,191.63V152h24a8,8,0,0,0,0-16H136V112a16,16,0,0,1,16-16h16a8,8,0,0,0,0-16H152a32,32,0,0,0-32,32v24H96a8,8,0,0,0,0,16h24v63.63a88,88,0,1,1,16,0Z"
                      ></path>
                    </svg>
                  </div>
                </a>
              </div>
              <p class="text-[#90adcb] text-base font-normal leading-normal">© 2024 Smokey AI. All rights reserved.</p>
            </footer>
          </div>
        </footer>
      </div>
    </div>
  </body>
</html>
