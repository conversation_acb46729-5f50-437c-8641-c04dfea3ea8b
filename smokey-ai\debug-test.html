<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Test - Smokey AI</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #0f172a;
            color: #f1f5f9;
            padding: 20px;
        }
        .debug-section {
            background: #1e293b;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid #334155;
        }
        .error { color: #ef4444; }
        .success { color: #10b981; }
        .warning { color: #f59e0b; }
    </style>
</head>
<body>
    <h1>🔧 Smokey AI Debug Test</h1>
    
    <div class="debug-section">
        <h2>JavaScript Loading Test</h2>
        <div id="config-status">Loading config.js...</div>
        <div id="app-status">Loading app.js...</div>
        <div id="init-status">Initializing app...</div>
    </div>

    <div class="debug-section">
        <h2>Console Output</h2>
        <div id="console-output"></div>
    </div>

    <script>
        // Capture console output
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(type, message) {
            const div = document.createElement('div');
            div.className = type;
            div.textContent = `[${type.toUpperCase()}] ${message}`;
            consoleOutput.appendChild(div);
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole('success', args.join(' '));
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole('error', args.join(' '));
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole('warning', args.join(' '));
        };
        
        // Test config loading
        try {
            document.getElementById('config-status').innerHTML = '<span class="success">✅ Starting config.js load...</span>';
        } catch (error) {
            document.getElementById('config-status').innerHTML = '<span class="error">❌ Error before config.js: ' + error.message + '</span>';
        }
    </script>

    <!-- Load config first -->
    <script src="config.js"></script>
    
    <script>
        try {
            document.getElementById('config-status').innerHTML = '<span class="success">✅ config.js loaded successfully</span>';
            document.getElementById('app-status').innerHTML = '<span class="success">✅ Starting app.js load...</span>';
        } catch (error) {
            document.getElementById('config-status').innerHTML = '<span class="error">❌ Error after config.js: ' + error.message + '</span>';
        }
    </script>

    <!-- Load app.js -->
    <script src="app.js"></script>
    
    <script>
        try {
            document.getElementById('app-status').innerHTML = '<span class="success">✅ app.js loaded successfully</span>';
            document.getElementById('init-status').innerHTML = '<span class="success">✅ All scripts loaded, waiting for DOM...</span>';
        } catch (error) {
            document.getElementById('app-status').innerHTML = '<span class="error">❌ Error after app.js: ' + error.message + '</span>';
        }
        
        // Test DOM ready
        document.addEventListener('DOMContentLoaded', function() {
            try {
                document.getElementById('init-status').innerHTML = '<span class="success">✅ DOM ready, app should initialize</span>';
                
                // Check if SmokeyAI class exists
                if (typeof SmokeyAI !== 'undefined') {
                    addToConsole('success', 'SmokeyAI class found');
                } else {
                    addToConsole('error', 'SmokeyAI class not found');
                }
                
                // Check if config is loaded
                if (typeof getConfig !== 'undefined') {
                    addToConsole('success', 'getConfig function found');
                    const config = getConfig();
                    addToConsole('success', 'Config loaded: ' + JSON.stringify(config.app));
                } else {
                    addToConsole('error', 'getConfig function not found');
                }
                
            } catch (error) {
                document.getElementById('init-status').innerHTML = '<span class="error">❌ Error during DOM ready: ' + error.message + '</span>';
                addToConsole('error', 'DOM ready error: ' + error.message);
            }
        });
        
        // Catch any unhandled errors
        window.addEventListener('error', function(event) {
            addToConsole('error', 'Unhandled error: ' + event.error.message + ' at line ' + event.lineno);
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            addToConsole('error', 'Unhandled promise rejection: ' + event.reason);
        });
    </script>
</body>
</html>
