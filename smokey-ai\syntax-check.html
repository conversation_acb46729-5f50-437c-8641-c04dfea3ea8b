<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Syntax Check - Smokey AI</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #0f172a;
            color: #f1f5f9;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .success {
            background: #064e3b;
            border-color: #10b981;
            color: #6ee7b7;
        }
        .error {
            background: #7f1d1d;
            border-color: #ef4444;
            color: #fca5a5;
        }
        .warning {
            background: #78350f;
            border-color: #f59e0b;
            color: #fbbf24;
        }
        .info {
            background: #1e3a8a;
            border-color: #3b82f6;
            color: #93c5fd;
        }
        pre {
            background: #1e293b;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            border: 1px solid #334155;
        }
        .test-btn {
            background: #8b5cf6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-btn:hover {
            background: #7c3aed;
        }
    </style>
</head>
<body>
    <h1>🔧 Smokey AI Syntax Check</h1>
    
    <div id="results"></div>
    
    <div class="status info">
        <h3>Manual Tests</h3>
        <button class="test-btn" onclick="testBasicFunctionality()">Test Basic Functions</button>
        <button class="test-btn" onclick="testEventListeners()">Test Event Listeners</button>
        <button class="test-btn" onclick="testConfig()">Test Config</button>
        <button class="test-btn" onclick="window.open('index.html', '_blank')">Open Main App</button>
    </div>

    <script>
        const results = document.getElementById('results');
        
        function addResult(type, title, message) {
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<h3>${title}</h3><p>${message}</p>`;
            results.appendChild(div);
        }
        
        function runSyntaxCheck() {
            addResult('info', '🔍 Starting Syntax Check', 'Checking JavaScript files for syntax errors...');
            
            // Test 1: Check if config.js loads
            try {
                if (typeof getConfig === 'function') {
                    addResult('success', '✅ config.js', 'Configuration file loaded successfully');
                } else {
                    addResult('error', '❌ config.js', 'Configuration functions not found');
                }
            } catch (error) {
                addResult('error', '❌ config.js Error', error.message);
            }
            
            // Test 2: Check if SmokeyAI class exists
            try {
                if (typeof SmokeyAI === 'function') {
                    addResult('success', '✅ SmokeyAI Class', 'Main class definition found');
                } else {
                    addResult('error', '❌ SmokeyAI Class', 'Main class not found');
                }
            } catch (error) {
                addResult('error', '❌ SmokeyAI Class Error', error.message);
            }
            
            // Test 3: Check critical functions
            try {
                const testInstance = new SmokeyAI();
                addResult('success', '✅ Class Instantiation', 'SmokeyAI can be instantiated');
                
                // Check if critical methods exist
                const methods = ['sendMessage', 'displayMessage', 'scrollToBottom', 'updateUserDisplay'];
                methods.forEach(method => {
                    if (typeof testInstance[method] === 'function') {
                        addResult('success', `✅ Method: ${method}`, 'Method exists and is callable');
                    } else {
                        addResult('error', `❌ Method: ${method}`, 'Method not found');
                    }
                });
                
            } catch (error) {
                addResult('error', '❌ Class Instantiation Error', error.message);
            }
            
            // Test 4: Check DOM elements
            const requiredElements = ['input', 'output', 'send-button'];
            requiredElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    addResult('warning', `⚠️ Element: ${id}`, 'Element found (but this is test page)');
                } else {
                    addResult('info', `ℹ️ Element: ${id}`, 'Element not found (expected in test page)');
                }
            });
        }
        
        function testBasicFunctionality() {
            addResult('info', '🧪 Testing Basic Functionality', 'Running basic function tests...');
            
            try {
                // Test config
                const config = getConfig();
                addResult('success', '✅ Config Test', `Version: ${config.app.version}, Groq: ${config.groq.enabled ? 'enabled' : 'disabled'}`);
                
                // Test utility functions
                if (typeof copyCode === 'function') {
                    addResult('success', '✅ Utility Functions', 'Global functions available');
                } else {
                    addResult('warning', '⚠️ Utility Functions', 'Some global functions may be missing');
                }
                
            } catch (error) {
                addResult('error', '❌ Basic Functionality Error', error.message);
            }
        }
        
        function testEventListeners() {
            addResult('info', '🎯 Testing Event Listeners', 'Checking event handling...');
            
            try {
                // Create test elements
                const testInput = document.createElement('textarea');
                const testButton = document.createElement('button');
                
                testInput.id = 'test-input';
                testButton.id = 'test-button';
                
                document.body.appendChild(testInput);
                document.body.appendChild(testButton);
                
                // Test event listener attachment
                let eventWorked = false;
                testButton.addEventListener('click', () => {
                    eventWorked = true;
                });
                
                testButton.click();
                
                if (eventWorked) {
                    addResult('success', '✅ Event Listeners', 'Event handling works correctly');
                } else {
                    addResult('error', '❌ Event Listeners', 'Event handling failed');
                }
                
                // Cleanup
                document.body.removeChild(testInput);
                document.body.removeChild(testButton);
                
            } catch (error) {
                addResult('error', '❌ Event Listener Error', error.message);
            }
        }
        
        function testConfig() {
            addResult('info', '⚙️ Testing Configuration', 'Checking configuration settings...');
            
            try {
                const config = getConfig();
                
                addResult('success', '✅ App Config', `Name: ${config.app.name}, Version: ${config.app.version}`);
                addResult('success', '✅ Groq Config', `Enabled: ${config.groq.enabled}, Model: ${config.groq.defaultModel}`);
                addResult('success', '✅ DeepSeek Config', `Enabled: ${config.deepseek.enabled}, Model: ${config.deepseek.defaultModel}`);
                addResult('success', '✅ Image Gen Config', `Enabled: ${config.imageGeneration.enabled}, Provider: ${config.imageGeneration.provider}`);
                
            } catch (error) {
                addResult('error', '❌ Configuration Error', error.message);
            }
        }
        
        // Catch any global errors
        window.addEventListener('error', function(event) {
            addResult('error', '❌ Global Error', `${event.error.message} at line ${event.lineno} in ${event.filename}`);
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            addResult('error', '❌ Promise Rejection', event.reason);
        });
        
        // Run syntax check when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(runSyntaxCheck, 100);
        });
    </script>
    
    <!-- Load the actual files -->
    <script src="config.js"></script>
    <script src="app.js"></script>
</body>
</html>
