<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Send Message Debug - Smokey AI</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #0f172a;
            color: #f1f5f9;
            padding: 20px;
            max-width: 1000px;
            margin: 0 auto;
        }
        .debug-section {
            background: #1e293b;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid #334155;
        }
        .status {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .success { background: #064e3b; border-color: #10b981; color: #6ee7b7; }
        .error { background: #7f1d1d; border-color: #ef4444; color: #fca5a5; }
        .warning { background: #78350f; border-color: #f59e0b; color: #fbbf24; }
        .info { background: #1e3a8a; border-color: #3b82f6; color: #93c5fd; }
        
        .test-input {
            width: 100%;
            padding: 10px;
            background: #334155;
            border: 1px solid #64748b;
            border-radius: 5px;
            color: #f1f5f9;
            margin: 10px 0;
        }
        .test-button {
            background: #8b5cf6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover { background: #7c3aed; }
        
        pre {
            background: #0f172a;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border: 1px solid #334155;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔧 Send Message Debug Tool</h1>
    
    <div class="debug-section">
        <h2>🎯 Quick Test</h2>
        <input type="text" class="test-input" id="test-input" placeholder="Type a test message here...">
        <button class="test-button" onclick="testSendMessage()">🚀 Test Send Message</button>
        <button class="test-button" onclick="testEnterKey()">⌨️ Test Enter Key</button>
        <button class="test-button" onclick="checkElements()">🔍 Check Elements</button>
    </div>
    
    <div class="debug-section">
        <h2>📊 Debug Results</h2>
        <div id="debug-results"></div>
    </div>
    
    <div class="debug-section">
        <h2>🔗 Navigation</h2>
        <button class="test-button" onclick="window.open('index.html', '_blank')">Open Main App</button>
        <button class="test-button" onclick="window.location.reload()">Refresh Debug</button>
    </div>

    <script>
        const debugResults = document.getElementById('debug-results');
        
        function addResult(type, title, message, details = '') {
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `
                <strong>${title}</strong><br>
                ${message}
                ${details ? `<pre>${details}</pre>` : ''}
            `;
            debugResults.appendChild(div);
        }
        
        function checkElements() {
            addResult('info', '🔍 Checking DOM Elements', 'Scanning for required elements...');
            
            // Check if we're on the main page or debug page
            const isMainPage = window.location.pathname.includes('index.html');
            
            if (!isMainPage) {
                addResult('warning', '⚠️ Not Main Page', 'This is the debug page. Elements may not exist here.');
            }
            
            // Check for required elements
            const requiredElements = [
                'input',
                'send-button', 
                'output',
                'user-name',
                'profile-btn',
                'tools-btn',
                'image-generator-btn'
            ];
            
            let foundElements = 0;
            let missingElements = [];
            
            requiredElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    foundElements++;
                    addResult('success', `✅ Element Found: ${id}`, `Element exists and is accessible`);
                } else {
                    missingElements.push(id);
                    addResult('error', `❌ Element Missing: ${id}`, `Element not found in DOM`);
                }
            });
            
            addResult('info', '📊 Element Summary', `Found: ${foundElements}/${requiredElements.length} elements`, 
                `Missing: ${missingElements.join(', ')}`);
        }
        
        function testSendMessage() {
            addResult('info', '🚀 Testing Send Message', 'Attempting to test send functionality...');
            
            try {
                // Check if SmokeyAI class exists
                if (typeof SmokeyAI === 'undefined') {
                    addResult('error', '❌ SmokeyAI Class Missing', 'SmokeyAI class not found. App may not be loaded.');
                    return;
                }
                
                // Check if global smokeyApp exists
                if (typeof smokeyApp === 'undefined') {
                    addResult('warning', '⚠️ Global App Instance Missing', 'Creating test instance...');
                    
                    // Try to create a test instance
                    try {
                        const testApp = new SmokeyAI();
                        addResult('success', '✅ Test Instance Created', 'SmokeyAI instance created successfully');
                        
                        // Test the sendMessage method
                        if (typeof testApp.sendMessage === 'function') {
                            addResult('success', '✅ sendMessage Method Found', 'Method exists and is callable');
                        } else {
                            addResult('error', '❌ sendMessage Method Missing', 'Method not found in instance');
                        }
                        
                    } catch (error) {
                        addResult('error', '❌ Instance Creation Failed', error.message);
                    }
                } else {
                    addResult('success', '✅ Global App Instance Found', 'smokeyApp is available');
                    
                    // Test with existing instance
                    if (typeof smokeyApp.sendMessage === 'function') {
                        addResult('success', '✅ sendMessage Available', 'Method ready to use');
                        
                        // Try to call it with test message
                        const testMessage = document.getElementById('test-input').value || 'Hello test';
                        addResult('info', '📤 Attempting Send', `Testing with message: "${testMessage}"`);
                        
                        // Note: We won't actually call it to avoid errors
                        addResult('info', '💡 Test Complete', 'Send method is available and should work');
                        
                    } else {
                        addResult('error', '❌ sendMessage Not Available', 'Method not found in global instance');
                    }
                }
                
            } catch (error) {
                addResult('error', '❌ Test Failed', error.message);
            }
        }
        
        function testEnterKey() {
            addResult('info', '⌨️ Testing Enter Key', 'Simulating Enter key press...');
            
            try {
                const testInput = document.getElementById('test-input');
                
                // Create and dispatch Enter key event
                const enterEvent = new KeyboardEvent('keydown', {
                    key: 'Enter',
                    code: 'Enter',
                    keyCode: 13,
                    which: 13,
                    bubbles: true
                });
                
                let eventFired = false;
                testInput.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter') {
                        eventFired = true;
                        addResult('success', '✅ Enter Event Detected', 'Keydown event fired successfully');
                    }
                });
                
                testInput.dispatchEvent(enterEvent);
                
                if (!eventFired) {
                    addResult('warning', '⚠️ Enter Event Not Detected', 'Event may not have fired properly');
                }
                
                addResult('info', '💡 Enter Key Test Complete', 'Event simulation finished');
                
            } catch (error) {
                addResult('error', '❌ Enter Key Test Failed', error.message);
            }
        }
        
        // Auto-run basic checks when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                addResult('info', '🔄 Auto-Check Starting', 'Running automatic diagnostics...');
                checkElements();
            }, 500);
        });
        
        // Catch any errors
        window.addEventListener('error', function(event) {
            addResult('error', '❌ JavaScript Error', `${event.error.message} at line ${event.lineno}`);
        });
    </script>
    
    <!-- Load the actual Smokey AI files for testing -->
    <script src="config.js"></script>
    <script src="app.js"></script>
</body>
</html>
